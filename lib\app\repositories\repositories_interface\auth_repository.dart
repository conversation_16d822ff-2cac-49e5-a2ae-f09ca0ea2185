



import 'package:deewan/app/data/models/entities/account_models.dart';

abstract class AuthenticationRepository {
  Future<UserPofile?> getCurrentUserProfile();
  Future<bool> login(String email, String password);
  Future<bool> register(String email, String password, String phone);
  Future<bool> verifyEmail(String token);
  Future<bool> verifyPhone(String code);
  Future<bool> logout();
  Future<bool> refreshToken();
  Future<bool> resetPassword(String email);
  Stream<AuthStatus> get authStatusStream;
  Future<bool> deleteAccount();
  Future<bool> updatePassword(String oldPassword, String newPassword);
}