import 'dart:async';
import 'package:deewan/app/data/models/entities/account_models.dart';
import 'package:deewan/app/data/models/entities/identity_model.dart';

abstract class UserProfileRepositoryInterface {
  Stream<UserProfile?> watchCurrentUserProfile();
  Stream<List<MyIdentity>> watchUserIdentities(String userProfileId);
  Stream<UserProfile?> watchUserProfile(String userProfileId);
  
  Future<UserProfile?> getCurrentUserProfile();
  Future<UserProfile?> getUserProfileById(String userProfileId);
  Future<List<MyIdentity>> getUserIdentities(String userProfileId);
  Future<MyIdentity?> getDefaultIdentity(String userProfileId);
  
  Future<void> saveUserProfile(UserProfile userProfile);
  Future<void> updateUserProfile(UserProfile userProfile);
  Future<void> updateLastActiveAt(String userProfileId);
  
  Future<MyIdentity> createMyIdentity(String userProfileId, String identityId, 
      {String? userName, String? title, bool isDefault = false});
  Future<void> updateMyIdentity(MyIdentity myIdentity);
  Future<void> deleteMyIdentity(String myIdentityId);
  Future<void> setDefaultIdentity(String myIdentityId, String userProfileId);
  
  Future<bool> verifyUserCredentials(String identifier, String pinCode);
  Future<void> updateVerificationStatus(String userProfileId, 
      {bool? emailVerified, bool? phoneVerified, bool? isVerified});
}