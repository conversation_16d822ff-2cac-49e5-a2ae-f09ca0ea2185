import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';

/// Enum to represent the connection status.
enum WebSocketStatus { connected, disconnected, connecting }

/// Abstract contract for a low-level WebSocket client.
/// This client's responsibility is to manage the raw connection and data stream.
/// A higher-level service (like your `WebSocketService`) will use this client
/// to implement application-specific logic (e.g., joining rooms, sending typing indicators).
abstract class WebSocketClient {
  /// Connects to the WebSocket server at the given URL.
  Future<void> connect(String url, {Map<String, String>? headers});

  /// Disconnects from the server.
  void disconnect();

  /// Sends a message to the server. The message should be a JSON-encodable map.
  void sendMessage(Map<String, dynamic> message);

  /// A stream of incoming messages, decoded from JSON.
  Stream<Map<String, dynamic>> get messages;

  /// A stream of the current connection status.
  Stream<WebSocketStatus> get status;

  /// Disposes of the client and its resources.
  void dispose();
}

/// Implementation of [WebSocketClient] using the `web_socket_channel` package.
class WebSocketClientImpl implements WebSocketClient {
  WebSocketChannel? _channel;
  final StreamController<Map<String, dynamic>> _messageController = StreamController.broadcast();
  final StreamController<WebSocketStatus> _statusController = StreamController.broadcast();

  @override
  Stream<Map<String, dynamic>> get messages => _messageController.stream;

  @override
  Stream<WebSocketStatus> get status => _statusController.stream;

  @override
  Future<void> connect(String url, {Map<String, String>? headers}) async {
    if (_channel != null) return; // Already connected or in the process

    _statusController.add(WebSocketStatus.connecting);
    try {
      _channel = WebSocketChannel.connect(Uri.parse(url), headers: headers);
      _statusController.add(WebSocketStatus.connected);

      _channel!.stream.listen(
        (message) {
          try {
            final decodedMessage = jsonDecode(message) as Map<String, dynamic>;
            _messageController.add(decodedMessage);
          } catch (e) {
            _messageController.addError(ArgumentError('Failed to decode JSON message: $message'));
          }
        },
        onDone: () {
          _statusController.add(WebSocketStatus.disconnected);
          _channel = null; // Clear the channel on disconnect
        },
        onError: (error) {
          _statusController.add(WebSocketStatus.disconnected);
          _messageController.addError(error);
          _channel = null; // Clear the channel on error
        },
      );
    } catch (e) {
      _statusController.add(WebSocketStatus.disconnected);
      _messageController.addError(e);
    }
  }

  @override
  void disconnect() => _channel?.sink.close();

  @override
  void sendMessage(Map<String, dynamic> message) {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode(message));
    } else {
      // In a real app, you might queue this message to be sent upon reconnection.
      throw Exception('WebSocket is not connected. Cannot send message.');
    }
  }

  @override
  void dispose() {
    _channel?.sink.close();
    _messageController.close();
    _statusController.close();
  }
}
