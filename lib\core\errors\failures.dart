import 'package:equatable/equatable.dart';

/// A base class for user-facing failures.
/// The UI layer will interact with these.
abstract class Failure extends Equatable {
  final String message;

  const Failure({required this.message});

  @override
  List<Object> get props => [message];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({required super.message});

}

class NetworkFailure extends Failure {
  const NetworkFailure({required super.message});
  
}

