
enum AppLanguage { english, arabic, }
enum AppMode { dev, prod }
enum AppOrientation { portrait, landscape }
enum AppThemeMode { light, dark, system }
enum StatusRequest {success,failure, serverFailure, offlineFailure,}
enum ConnectionStatus { connected, disconnected, connecting, reconnecting, failed }
enum AuthStatus { authenticated, unauthenticated, loading, error }
enum PresenceStatus { online, offline, away, busy, invisible }
enum NetworkStatus { online, offline, limited, unknown }
enum NetworkQuality { excellent, good, fair, poor }
enum FileType { image, video, audio, document, any }
enum SyncStatus { idle, syncing, completed, failed, paused }
enum QueueStatus { idle, processing, paused, error }
enum Permission { camera, microphone, storage, location, contacts, notifications }
enum PermissionResult { granted, denied, restricted, permanentlyDenied }
enum SearchType { message, room, contact, media }
enum RecordingState { idle, recording, paused, stopped }
enum AudioPlaybackState { idle, playing, paused, stopped, loading }
enum BackupFrequency { daily, weekly, monthly }
enum ContactSyncStatus { idle, syncing, completed, failed }
enum PermissionStatus { granted, denied, restricted, permanentlyDenied }


enum ImageFormat { jpeg, png, webp }
enum AudioFormat { mp3, aac, wav }
enum ThemeMode { light, dark, system }