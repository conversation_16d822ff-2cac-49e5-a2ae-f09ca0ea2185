import 'dart:ui';
import 'package:deewan/app/services/services_impl/appservices.dart';
import 'package:get/get.dart';
import '../../../data/models/entities/sittings_model.dart';

class SettingsController extends GetxController {
  Locale? language;
  Sittings savedLanguage = Sittings(id: 1);

  ObjectboxService objectboxService = Get.find<ObjectboxService>();
//  var appDataBox ;

  changeLang(String lang) {
    Locale locale = Locale(lang);
    savedLanguage.language = lang;
    objectboxService.objectbox.appBox.put(savedLanguage);
    Get.updateLocale(locale);
    print(savedLanguage.language);
  }

  @override
  void onInit() {
    // changeLang(savedLanguage.language.toString());
    // objectboxService.objectbox.appBox.put(savedLanguage);
    String? lang =
        objectboxService.objectbox.appBox.get(savedLanguage.id)?.language;
    if (lang == "ar") {
      language = const Locale("ar");
    } else if (lang == "en") {
      language = const Locale("en");
    } else {
      language = Locale(Get.deviceLocale!.languageCode);

      super.onInit();
    }
  }
}
