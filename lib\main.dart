import 'package:deewan/app/bindings/initial_bindings.dart';
import 'package:deewan/app/presentation/pages/sittings/settingscontroller.dart';
import 'package:deewan/core/localization/translation.dart';
import 'package:deewan/app/services/services_impl/appservices.dart';
import 'package:deewan/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
// final service = Get.put(GetxServiceAsync());
//   await service.ensureInitialization<GetxServiceAsync>();

  await initServices();
  runApp(Deewan());
}

// ignore: must_be_immutable

class <PERSON>wan extends StatelessWidget {
  static String title = "title".tr;

  const Deewan({super.key});
  @override
  Widget build(BuildContext context) {

    //SettingsController settingsController = Get.put(SettingsController());
    var settingsController = Get.put(SettingsController());
    return GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: title,
        theme: ThemeData(
          primaryColorDark: Color(0xFF1E1E1E),
        ),
        translations: AppTranslation(),
         
        locale: settingsController.language,
        fallbackLocale: Locale('ar', 'JO'),
        initialRoute: routes?[0].name,
          

        getPages: routes,
        // ignore: dead_code

        defaultTransition: Transition.fade,
        binds: Initialbindings().dependencies(),
);
  }

}
