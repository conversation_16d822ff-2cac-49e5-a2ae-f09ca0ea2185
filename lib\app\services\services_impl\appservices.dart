import 'package:get/get.dart';
import '../../../core/utils/classes/objectbox.dart';

abstract class InitializableService extends GetxService {
  bool _initialized = false;
  Future<void> _init(); // Abstract method for specific initialization
  Future<dynamic> ensureInitialized() async {
    if (!_initialized) {
      await _init();
      _initialized = true;
    }
    return this;
  }
}

class ObjectboxService extends InitializableService {

  late ObjectBox objectbox;

  @override
  Future<ObjectboxService> _init() async {
    objectbox = await ObjectBox.create();
     

    return this;
  }
  //func to open box allerdy in objectbox class
}

class SettingsService extends InitializableService {
  @override
  Future<void> _init() async {}
}

Future<void> initServices() async {
   
  print('starting services ...');

  Get.put(() => ObjectboxService().ensureInitialized());
  Get.put(SettingsService().ensureInitialized());
  
  print('All services started...');
}

// class ObjectboxService extends GetxService {
//   late ObjectBox objectbox;
//   bool _initialized = false;

//   Future<ObjectboxService> init() async {
//      objectbox = await ObjectBox.create();
//     return this;
//   }
//   Future<ObjectboxService> ensureInitialization() async {
//     if (!_initialized) {
//       await init();
//       _initialized = true;
//     }
//     return this;
//   }
// }
// class SettingsService extends GetxService {
//   bool _initialized = false;
//    Future<void> init() async {
//    //put the func here
//     }
//       Future<dynamic> ensureInitialization() async {
//       if (!_initialized) {
//         await init();
//         _initialized = true;
//       }
//       return this;
//   }
// }

// Future<void> initServices() async {
//   print('starting services ...');
//   //Get.put<ApiProvider>(ApiProvider());
//   /// Here is where you put get_storage, hive, shared_pref initialization.
//   /// or moor connection, or whatever that's async.
  
//    Get.put(() => ObjectboxService().ensureInitialization());
//    Get.put(SettingsService()).ensureInitialization();

//   print('All services started...');
// }

// to review....